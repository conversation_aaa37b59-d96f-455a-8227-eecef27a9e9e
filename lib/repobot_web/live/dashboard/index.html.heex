<div class="p-6">
  <div class="flex justify-between items-center mb-8">
    <h1 class="text-2xl font-semibold text-slate-900">Dashboard</h1>
  </div>

  <div class="space-y-6">
    <!-- Repository Insights - Full Width -->
    <div class="rounded-lg border border-slate-200 bg-white shadow">
      <div class="p-4 border-b border-slate-200">
        <h3 class="text-lg font-medium">Repository Insights</h3>
        <p class="text-sm text-slate-500">Key metrics from your GitHub repositories</p>
      </div>
      <div class="p-4">
        <div class="space-y-6">
          <!-- First Row: Metrics -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="space-y-2">
              <div class="text-sm font-medium text-slate-500">Repositories</div>
              <div class="text-2xl font-bold">{@repo_insights.total_repos}</div>
              <div class="text-xs text-slate-500">
                <span class="inline-flex items-center">
                  <span class="w-2 h-2 rounded-full bg-accent mr-1"></span>
                  {@repo_insights.template_repos} templates
                </span>
                <span class="ml-2 inline-flex items-center">
                  <span class="w-2 h-2 rounded-full bg-base-300 mr-1"></span>
                  {@repo_insights.private_repos} private
                </span>
              </div>
            </div>
            <div class="space-y-2">
              <div class="text-sm font-medium text-slate-500">Pull Requests</div>
              <div class="text-2xl font-bold">{@repo_insights.open_prs}</div>
              <div class="text-xs text-slate-500">
                <span class="inline-flex items-center">
                  <span class="w-2 h-2 rounded-full bg-success mr-1"></span>
                  {@repo_insights.open_prs} open
                </span>
                <span class="ml-2 inline-flex items-center">
                  <span class="w-2 h-2 rounded-full bg-secondary mr-1"></span>
                  {@repo_insights.merged_prs} merged
                </span>
              </div>
            </div>
            <div class="space-y-2">
              <div class="text-sm font-medium text-slate-500">Sync Status</div>
              <div class="text-2xl font-bold">{@repo_insights.sync_success_rate}%</div>
              <div class="text-xs text-slate-500">
                <span class="inline-flex items-center">
                  <span class="w-2 h-2 rounded-full bg-success mr-1"></span>
                  {@repo_insights.successful_syncs} successful
                </span>
                <span class="ml-2 inline-flex items-center">
                  <span class="w-2 h-2 rounded-full bg-error mr-1"></span>
                  {@repo_insights.failed_syncs} failed
                </span>
              </div>
            </div>
            <div class="space-y-2">
              <div class="text-sm font-medium text-slate-500">GitHub Activity</div>
              <div class="text-2xl font-bold">
                {Enum.map(@contribution_data, & &1.value) |> Enum.sum()}
              </div>
              <div class="text-xs text-slate-500">
                <span class="inline-flex items-center">
                  <span class="w-2 h-2 rounded-full bg-primary mr-1"></span> Push & PR events
                </span>
              </div>
            </div>
          </div>
          
<!-- Second Row: Programming Languages -->
          <div class="border-t border-slate-100 pt-4">
            <h4 class="text-sm font-medium text-slate-500 mb-3">Programming Languages</h4>
            <div class="flex flex-wrap gap-2">
              <%= for {language, count} <- @repo_insights.languages do %>
                <div class="px-2 py-1 bg-slate-100 rounded text-xs flex items-center">
                  <div class={language_color(language)}></div>
                  <span class="ml-1.5">{language}</span>
                  <span class="ml-1 text-slate-500">({count})</span>
                </div>
              <% end %>
              <%= if Enum.empty?(@repo_insights.languages) do %>
                <div class="text-slate-500 text-xs">No language data available</div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
    
<!-- Recent Events - Full Width -->
    <div class="rounded-lg border border-slate-200 bg-white shadow">
      <div class="p-4 border-b border-slate-200">
        <h3 class="text-lg font-medium">Recent Events</h3>
        <p class="text-sm text-slate-500">
          Template pushes, sync pull requests, and direct push syncs
        </p>
      </div>
      <div class="p-4">
        <div class="space-y-4">
          <%= if Enum.empty?(@recent_activity) do %>
            <div class="p-4 text-center text-slate-500">
              <p>No recent events found.</p>
            </div>
          <% else %>
            <%= for item <- @recent_activity do %>
              <div
                class="flex items-start space-x-4 border-b pb-4 last:border-0 last:pb-0"
                data-test-event-type={item.event_type}
              >
                <div class="h-9 w-9 rounded-full flex items-center justify-center {event_icon_bg(item.event_type)}">
                  <%= if item.user.avatar_url do %>
                    <img
                      src={item.user.avatar_url}
                      alt={item.user.login}
                      class="h-9 w-9 rounded-full"
                    />
                  <% else %>
                    <%= if item.event_type == "sync_direct" do %>
                      <.icon name="hero-arrow-path" class="h-5 w-5 text-white" />
                    <% else %>
                      <div class="text-slate-700 font-semibold">{item.user.initials}</div>
                    <% end %>
                  <% end %>
                </div>
                <div class="flex-1 space-y-1">
                  <div class="flex items-center flex-wrap">
                    <span class="font-medium">{item.user.login}</span>
                    <span class="ml-1 text-slate-500">{item.action}</span>
                  </div>
                  <div class="text-sm text-slate-500">
                    <span>in {item.repository}</span>
                    <span class="ml-2 text-xs">{item.date}</span>
                  </div>
                  <%= if item.commit_message do %>
                    <div class="mt-1 p-2 bg-slate-50 rounded text-sm border-l-2 border-indigo-300">
                      <span class="text-slate-700">{item.commit_message}</span>
                    </div>
                  <% end %>
                  <%= if Enum.any?(item.modified_files) do %>
                    <div class="mt-2 text-xs text-slate-500">
                      <div class="font-medium mb-1">Modified files:</div>
                      <div class="flex flex-wrap gap-1">
                        <%= for file <- item.modified_files do %>
                          <span class="bg-slate-100 text-slate-700 px-2 py-1 rounded">
                            {file}
                          </span>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
                <.btn href={"/repositories/#{item.id}"} variant="ghost" size="sm" circle>
                  <.icon name="hero-arrow-top-right-on-square" class="h-4 w-4" />
                </.btn>
              </div>
            <% end %>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>
