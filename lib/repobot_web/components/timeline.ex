defmodule RepobotWeb.Components.Timeline do
  use Phoenix.Component

  @doc """
  Renders a timeline component with sync events.

  ## Examples

      <.timeline events={@recent_activity} />

      <.timeline events={@recent_activity} max_items={3} />
  """
  attr :events, :list, required: true, doc: "List of timeline events"
  attr :max_items, :integer, default: 5, doc: "Maximum number of items to display"

  attr :orientation, :string,
    default: "timeline-horizontal w-full",
    doc: "Timeline orientation classes"

  def timeline(assigns) do
    ~H"""
    <%= if Enum.empty?(@events) do %>
      <div class="p-4 text-center text-slate-500">
        <p>No recent sync activity found.</p>
      </div>
    <% else %>
      <ul class={"timeline #{@orientation}"}>
        <%= for {item, index} <- Enum.with_index(@events) |> Enum.take(@max_items) do %>
          <li>
            <%= if index > 0 do %>
              <hr class={get_timeline_hr_color(item.event_type)} />
            <% end %>

            <%= if rem(index, 2) == 0 do %>
              <!-- Even index: content on start side -->
              <div class="timeline-start">
                <div class="tooltip tooltip-bottom" data-tip={get_tooltip_content(item)}>
                  <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-3 cursor-pointer transition-all duration-200 hover:shadow-md hover:border-slate-300 flex items-center gap-3 min-w-48">
                    <!-- Icon -->
                    <div class="flex-shrink-0">
                      {get_timeline_box_icon(item.event_type)}
                    </div>
                    <!-- Content -->
                    <div class="flex-1 min-w-0">
                      <div class="text-sm font-medium text-slate-900 truncate">
                        {item.repository}
                      </div>
                      <div class="text-xs text-slate-500 mt-0.5">
                        {format_timeline_date(item.date)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="timeline-middle">
                {get_timeline_icon(item.event_type)}
              </div>
            <% else %>
              <!-- Odd index: content on end side -->
              <div class="timeline-middle">
                {get_timeline_icon(item.event_type)}
              </div>
              <div class="timeline-end">
                <div class="tooltip tooltip-top" data-tip={get_tooltip_content(item)}>
                  <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-3 cursor-pointer transition-all duration-200 hover:shadow-md hover:border-slate-300 flex items-center gap-3 min-w-48">
                    <!-- Icon -->
                    <div class="flex-shrink-0">
                      {get_timeline_box_icon(item.event_type)}
                    </div>
                    <!-- Content -->
                    <div class="flex-1 min-w-0">
                      <div class="text-sm font-medium text-slate-900 truncate">
                        {item.repository}
                      </div>
                      <div class="text-xs text-slate-500 mt-0.5">
                        {format_timeline_date(item.date)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            <% end %>

            <%= if index < @max_items - 1 and index < length(@events) - 1 do %>
              <hr class={get_timeline_hr_color(Enum.at(@events, index + 1).event_type)} />
            <% end %>
          </li>
        <% end %>
      </ul>
    <% end %>
    """
  end

  # Timeline helper functions
  defp get_timeline_hr_color(event_type) do
    case event_type do
      "push" -> "bg-primary"
      "pr_opened" -> "bg-success"
      "sync_direct" -> "bg-accent"
      _ -> ""
    end
  end

  defp get_tooltip_content(item) do
    base_content = "#{item.action} • #{item.repository}"

    if item.commit_message do
      "#{base_content} • #{item.commit_message}"
    else
      base_content
    end
  end

  defp get_timeline_box_icon(event_type) do
    case event_type do
      "push" ->
        Phoenix.HTML.raw("""
        <div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
          <span class="hero-arrow-up-tray w-4 h-4 text-primary"></span>
        </div>
        """)

      "pr_opened" ->
        Phoenix.HTML.raw("""
        <div class="w-8 h-8 bg-success/10 rounded-full flex items-center justify-center">
          <span class="hero-arrow-top-right-on-square w-4 h-4 text-success"></span>
        </div>
        """)

      "sync_direct" ->
        Phoenix.HTML.raw("""
        <div class="w-8 h-8 bg-accent/10 rounded-full flex items-center justify-center">
          <span class="hero-arrow-path w-4 h-4 text-accent"></span>
        </div>
        """)

      _ ->
        Phoenix.HTML.raw("""
        <div class="w-8 h-8 bg-base-300/10 rounded-full flex items-center justify-center">
          <span class="hero-plus-circle w-4 h-4 text-base-content"></span>
        </div>
        """)
    end
  end

  defp get_timeline_icon(event_type) do
    case event_type do
      "push" ->
        Phoenix.HTML.raw("""
        <div class="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
          <span class="hero-arrow-up-tray w-3 h-3 text-primary-content"></span>
        </div>
        """)

      "pr_opened" ->
        Phoenix.HTML.raw("""
        <div class="w-6 h-6 bg-success rounded-full flex items-center justify-center">
          <span class="hero-arrow-top-right-on-square w-3 h-3 text-success-content"></span>
        </div>
        """)

      "sync_direct" ->
        Phoenix.HTML.raw("""
        <div class="w-6 h-6 bg-accent rounded-full flex items-center justify-center">
          <span class="hero-arrow-path w-3 h-3 text-accent-content"></span>
        </div>
        """)

      _ ->
        Phoenix.HTML.raw("""
        <div class="w-6 h-6 bg-base-300 rounded-full flex items-center justify-center">
          <span class="hero-plus-circle w-3 h-3 text-base-content"></span>
        </div>
        """)
    end
  end

  defp format_timeline_date(date_string) do
    # Convert relative time to a short format for timeline
    case date_string do
      "just now" ->
        "now"

      date when is_binary(date) ->
        date
        |> String.replace(" ago", "")
        |> String.replace("minute", "min")
        |> String.replace("hour", "hr")
        |> String.replace("day", "d")
        |> String.replace("week", "w")
        |> String.replace("month", "mo")
        |> String.replace("year", "yr")
        |> String.replace("s", "")

      _ ->
        "unknown"
    end
  end
end
